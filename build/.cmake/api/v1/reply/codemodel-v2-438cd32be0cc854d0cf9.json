{"configurations": [{"directories": [{"build": ".", "jsonFile": "directory-.-Debug-f5ebdc15457944623624.json", "minimumCMakeVersion": {"string": "3.13.4"}, "projectIndex": 0, "source": ".", "targetIndexes": [0, 1, 2, 3, 4]}], "name": "Debug", "projects": [{"directoryIndexes": [0], "name": "FunctionLoggerPass", "targetIndexes": [0, 1, 2, 3, 4]}], "targets": [{"directoryIndex": 0, "id": "FunctionLoggerPass::@6890427a1f51a3e7e1df", "jsonFile": "target-FunctionLoggerPass-Debug-ddc5e7f50a1c811cae29.json", "name": "FunctionLoggerPass", "projectIndex": 0}, {"directoryIndex": 0, "id": "RISCVTargetParserTableGen::@6890427a1f51a3e7e1df", "jsonFile": "target-RISCVTargetParserTableGen-Debug-7161b54bacc1616de0f3.json", "name": "RISCVTargetParserTableGen", "projectIndex": 0}, {"directoryIndex": 0, "id": "acc_gen::@6890427a1f51a3e7e1df", "jsonFile": "target-acc_gen-Debug-2305b645e915aa77d1a6.json", "name": "acc_gen", "projectIndex": 0}, {"directoryIndex": 0, "id": "intrinsics_gen::@6890427a1f51a3e7e1df", "jsonFile": "target-intrinsics_gen-Debug-471f2f18a4acae285ae5.json", "name": "intrinsics_gen", "projectIndex": 0}, {"directoryIndex": 0, "id": "omp_gen::@6890427a1f51a3e7e1df", "jsonFile": "target-omp_gen-Debug-e384866de0e9391a2288.json", "name": "omp_gen", "projectIndex": 0}]}], "kind": "codemodel", "paths": {"build": "/home/<USER>/sqlite_loger/build", "source": "/home/<USER>/sqlite_loger/pass"}, "version": {"major": 2, "minor": 6}}