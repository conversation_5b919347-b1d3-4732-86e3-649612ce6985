<?xml version="1.0" encoding="UTF-8"?><!--
    Licensed to the Apache Software Foundation (ASF) under one
    or more contributor license agreements.  See the NOTICE file
    distributed with this work for additional information
    regarding copyright ownership.  The ASF licenses this file
    to you under the Apache License, Version 2.0 (the
    "License"); you may not use this file except in compliance
    with the License.  You may obtain a copy of the License at

      http://www.apache.org/licenses/LICENSE-2.0

    Unless required by applicable law or agreed to in writing,
    software distributed under the License is distributed on an
    "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
    KIND, either express or implied.  See the License for the
    specific language governing permissions and limitations
    under the License.
  -->

<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>org.apache</groupId>
        <artifactId>apache</artifactId>
        <version>21</version>
    </parent>

    <groupId>org.apache.curator</groupId>
    <artifactId>apache-curator</artifactId>
    <version>5.2.0</version>
    <packaging>pom</packaging>

    <name>Apache Curator</name>
    <description>
        Curator is a set of Java libraries that make using Apache ZooKeeper much easier.
    </description>
    <url>http://curator.apache.org</url>
    <inceptionYear>2011</inceptionYear>

    <licenses>
        <license>
            <name>The Apache Software License, Version 2.0</name>
            <url>http://www.apache.org/licenses/LICENSE-2.0.txt</url>
            <distribution>repo</distribution>
        </license>
    </licenses>

    <organization>
        <name>The Apache Software Foundation</name>
        <url>http://www.apache.org/</url>
    </organization>

    <properties>
        <!-- maven properties -->
        <maven.compiler.source>1.8</maven.compiler.source>
        <maven.compiler.target>1.8</maven.compiler.target>
        <dependency.locations.enabled>false</dependency.locations.enabled>

        <currentStableVersion>5.1.0</currentStableVersion>

        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.build.resourceEncoding>UTF-8</project.build.resourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>

        <short-jdk-version>8</short-jdk-version>
        <jdk-version>1.${short-jdk-version}</jdk-version>

        <redirectTestOutputToFile>true</redirectTestOutputToFile>

        <!-- versions -->
        <zookeeper-version>3.6.3</zookeeper-version>
        <maven-bundle-plugin-version>5.1.1</maven-bundle-plugin-version>
        <maven-javadoc-plugin-version>3.0.1</maven-javadoc-plugin-version>
        <doxia-module-confluence-version>1.8</doxia-module-confluence-version>
        <maven-license-plugin-version>1.9.0</maven-license-plugin-version>
        <javassist-version>3.24.1-GA</javassist-version>
        <commons-math-version>2.2</commons-math-version>
        <jackson-mapper-asl-version>1.9.13</jackson-mapper-asl-version>
        <jackson-version>2.10.0</jackson-version>
        <!-- Upgrading to Jersey 2.x is difficult and of unclear benefits, see
             https://stackoverflow.com/questions/17098341#22033825 -->
        <jersey-version>1.19.4</jersey-version>
        <jsr311-api-version>1.1.1</jsr311-api-version>
        <!-- See https://stackoverflow.com/questions/43574426#comment93992044_43574427 -->
        <jaxb-version>2.2.11</jaxb-version>
        <javax-activation-version>1.1.1</javax-activation-version>
        <jetty-version>6.1.26</jetty-version>
        <scannotation-version>1.0.2</scannotation-version>
        <!-- resteasy-jaxrs dependency cannot be higher than 2.x for compatibility with Jersey 1.x -->
        <resteasy-jaxrs-version>2.3.5.Final</resteasy-jaxrs-version>
        <guava-version>27.0.1-jre</guava-version>
        <guava-listenablefuture-version>1.0</guava-listenablefuture-version>
        <guava-failureaccess-version>1.0.1</guava-failureaccess-version>
        <junit-version>5.6.2</junit-version>
        <swift-version>0.23.1</swift-version>
        <maven-shade-plugin-version>3.2.1</maven-shade-plugin-version>
        <slf4j-version>1.7.25</slf4j-version>
        <clirr-maven-plugin-version>2.8</clirr-maven-plugin-version>
        <dropwizard-version>3.2.5</dropwizard-version>
        <snappy-version>1.1.7</snappy-version>
        <build-helper-maven-plugin-version>3.1.0</build-helper-maven-plugin-version>
        <awaitility-version>4.1.0</awaitility-version>

        <!-- OSGi Properties -->
        <osgi.export.package />
        <osgi.import.package />
        <osgi.private.package />
        <osgi.dynamic.import />
        <osgi.require.bundle />
        <osgi.export.service />
        <osgi.activator />
    </properties>

    <scm>
        <url>https://github.com/apache/curator.git</url>
        <connection>scm:git:https://gitbox.apache.org/repos/asf/curator.git</connection>
        <developerConnection>scm:git:https://gitbox.apache.org/repos/asf/curator.git
        </developerConnection>
        <tag>apache-curator-5.2.0</tag>
    </scm>

    <issueManagement>
        <system>JIRA</system>
        <url>http://issues.apache.org/jira/browse/CURATOR</url>
    </issueManagement>

    <ciManagement>
        <system>Jenkins</system>
        <url>https://builds.apache.org/job/Curator/</url>
        <notifiers>
            <notifier>
                <type>mail</type>
                <sendOnError>true</sendOnError>
                <sendOnFailure>true</sendOnFailure>
                <sendOnSuccess>false</sendOnSuccess>
                <sendOnWarning>false</sendOnWarning>
                <configuration>
                    <address><EMAIL></address>
                </configuration>
            </notifier>
        </notifiers>
    </ciManagement>

    <distributionManagement>
        <site>
            <id>apache.website.svnpub</id>
            <url>scm:svn:https://svn.apache.org/repos/asf/curator/site/trunk</url>
        </site>
    </distributionManagement>

    <mailingLists>
        <mailingList>
            <name>Users</name>
            <subscribe>mailto:<EMAIL></subscribe>
            <unsubscribe>mailto:<EMAIL></unsubscribe>
            <post>mailto:<EMAIL></post>
            <archive>http://mail-archives.apache.org/mod_mbox/curator-user/</archive>
        </mailingList>
        <mailingList>
            <name>Development</name>
            <subscribe>mailto:<EMAIL></subscribe>
            <unsubscribe>mailto:<EMAIL></unsubscribe>
            <post>mailto:<EMAIL></post>
            <archive>http://mail-archives.apache.org/mod_mbox/curator-dev/</archive>
        </mailingList>
        <mailingList>
            <name>Commits</name>
            <subscribe>mailto:<EMAIL></subscribe>
            <unsubscribe>mailto:<EMAIL></unsubscribe>
            <post>mailto:<EMAIL></post>
            <archive>http://mail-archives.apache.org/mod_mbox/curator-commits/</archive>
        </mailingList>
    </mailingLists>

    <developers>
        <developer>
            <id>randgalt</id>
            <name>Jordan Zimmerman</name>
            <email><EMAIL></email>
            <timezone>-5</timezone>
            <roles>
                <role>Committer</role>
                <role>PMC Member</role>
            </roles>
            <url>https://people.apache.org/~randgalt</url>
        </developer>

        <developer>
            <id>zarfide</id>
            <name>Jay Zarfoss</name>
            <email><EMAIL></email>
            <timezone>-8</timezone>
            <roles>
                <role>Committer</role>
                <role>PMC Member</role>
            </roles>
            <url>http://www.linkedin.com/pub/jay-zarfoss/34/56/a19</url>
        </developer>

        <developer>
            <id>cheddar</id>
            <name>Eric Tschetter</name>
            <email><EMAIL></email>
            <timezone>-6</timezone>
            <roles>
                <role>Committer</role>
                <role>PMC Member</role>
                <role>ChedHeader</role>
            </roles>
        </developer>
        <developer>
            <id>iocanel</id>
            <name>Ioannis Canellos</name>
            <email><EMAIL></email>
            <timezone>+2</timezone>
            <roles>
                <role>Committer</role>
                <role>PMC Member</role>
            </roles>
            <url>http://iocanel.blogspot.com</url>
        </developer>

        <developer>
            <id>cammckenzie</id>
            <name>Cameron McKenzie</name>
            <email><EMAIL></email>
            <timezone>+10</timezone>
            <roles>
                <role>Committer</role>
                <role>PMC Member</role>
            </roles>
            <url>https://people.apache.org/~cammckenzie</url>
        </developer>

        <developer>
            <id>dragonsinth</id>
            <name>Scott Blum</name>
            <email><EMAIL></email>
            <timezone>-5</timezone>
            <roles>
                <role>Committer</role>
                <role>PMC Member</role>
            </roles>
            <url>http://github.com/dragonsinth</url>
        </developer>

        <developer>
            <id>mdrob</id>
            <name>Mike Drob</name>
            <email><EMAIL></email>
            <timezone>-6</timezone>
            <roles>
                <role>Committer</role>
                <role>PMC Member</role>
            </roles>
            <url>http://people.apache.org/~mdrob</url>
        </developer>

        <developer>
            <name>Patrick Hunt</name>
            <email><EMAIL></email>
            <roles>
                <role>PMC Member</role>
            </roles>
            <timezone>-8</timezone>
            <url>http://www.linkedin.com/pub/patrick-hunt/2/5b2/24a</url>
        </developer>

        <developer>
            <name>Mahadev Konar</name>
            <email><EMAIL></email>
            <roles>
                <role>PMC Member</role>
            </roles>
            <timezone>-8</timezone>
            <url>http://www.linkedin.com/in/mahadevkonar</url>
        </developer>

        <developer>
            <name>Luciano Resende</name>
            <email><EMAIL></email>
            <roles>
                <role>PMC Member</role>
            </roles>
            <timezone>-8</timezone>
            <url>https://people.apache.org/~lresende</url>
        </developer>

        <developer>
            <name>Enis Söztutar</name>
            <email><EMAIL></email>
            <roles>
                <role>PMC Member</role>
            </roles>
            <timezone>-8</timezone>
            <url>https://people.apache.org/~enis</url>
        </developer>

        <developer>
            <name>Fangmin Lyu</name>
            <email><EMAIL></email>
            <roles>
                <role>Committer</role>
                <role>PMC Member</role>
            </roles>
            <timezone>-8</timezone>
            <url>https://people.apache.org/~fangmin</url>
        </developer>

        <developer>
            <id>shayshim</id>
            <name>Shay Shimony</name>
            <email><EMAIL></email>
            <timezone>+2</timezone>
            <roles>
                <role>Committer</role>
                <role>PMC Member</role>
            </roles>
            <url>https://people.apache.org/~shayshim</url>
        </developer>

        <developer>
            <id>eolivelli</id>
            <name>Enrico Olivelli</name>
            <email><EMAIL></email>
            <timezone>+1</timezone>
            <roles>
                <role>Committer</role>
                <role>PMC Chair</role>
            </roles>
            <url>https://people.apache.org/~eolivelli</url>
        </developer>

        <developer>
            <id>tison</id>
            <name>Zili Chen</name>
            <email><EMAIL></email>
            <timezone>+8</timezone>
            <roles>
                <role>Committer</role>
                <role>PMC Member</role>
            </roles>
            <url>https://github.com/tisonkun/</url>
        </developer>
    </developers>

    <modules>
        <module>curator-client</module>
        <module>curator-test</module>
        <module>curator-framework</module>
        <module>curator-recipes</module>
        <module>curator-examples</module>
        <module>curator-x-discovery</module>
        <module>curator-x-discovery-server</module>
        <module>curator-x-async</module>
        <module>curator-test-zk35</module>
    </modules>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>org.slf4j</groupId>
                <artifactId>slf4j-api</artifactId>
                <version>${slf4j-version}</version>
            </dependency>

            <dependency>
                <groupId>org.slf4j</groupId>
                <artifactId>slf4j-log4j12</artifactId>
                <version>${slf4j-version}</version>
            </dependency>

            <dependency>
                <groupId>org.mockito</groupId>
                <artifactId>mockito-core</artifactId>
                <version>1.9.5</version>
            </dependency>

            <dependency>
                <groupId>org.apache.curator</groupId>
                <artifactId>curator-client</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>org.apache.curator</groupId>
                <artifactId>curator-framework</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>org.apache.curator</groupId>
                <artifactId>curator-framework</artifactId>
                <type>test-jar</type>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>org.apache.curator</groupId>
                <artifactId>curator-recipes</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>org.apache.curator</groupId>
                <artifactId>curator-recipes</artifactId>
                <version>${project.version}</version>
                <type>test-jar</type>
            </dependency>

            <dependency>
                <groupId>org.apache.curator</groupId>
                <artifactId>curator-test</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>org.apache.curator</groupId>
                <artifactId>curator-x-discovery</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>org.apache.curator</groupId>
                <artifactId>curator-x-discovery-server</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>org.apache.curator</groupId>
                <artifactId>curator-x-async</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-math</artifactId>
                <version>${commons-math-version}</version>
            </dependency>

            <dependency>
                <groupId>com.fasterxml.jackson.core</groupId>
                <artifactId>jackson-core</artifactId>
                <version>${jackson-version}</version>
            </dependency>

            <dependency>
                <groupId>com.fasterxml.jackson.core</groupId>
                <artifactId>jackson-databind</artifactId>
                <version>${jackson-version}</version>
            </dependency>

            <dependency>
                <groupId>com.sun.jersey</groupId>
                <artifactId>jersey-server</artifactId>
                <version>${jersey-version}</version>
            </dependency>

            <dependency>
                <groupId>com.sun.jersey</groupId>
                <artifactId>jersey-servlet</artifactId>
                <version>${jersey-version}</version>
            </dependency>

            <dependency>
                <groupId>com.sun.jersey</groupId>
                <artifactId>jersey-client</artifactId>
                <version>${jersey-version}</version>
            </dependency>

            <dependency>
                <groupId>com.sun.jersey</groupId>
                <artifactId>jersey-core</artifactId>
                <version>${jersey-version}</version>
            </dependency>

            <dependency>
                <groupId>javax.ws.rs</groupId>
                <artifactId>jsr311-api</artifactId>
                <version>${jsr311-api-version}</version>
            </dependency>

            <dependency>
                <groupId>javax.xml.bind</groupId>
                <artifactId>jaxb-api</artifactId>
                <version>${jaxb-version}</version>
            </dependency>

            <dependency>
                <groupId>com.sun.xml.bind</groupId>
                <artifactId>jaxb-core</artifactId>
                <version>${jaxb-version}</version>
            </dependency>

            <dependency>
                <groupId>com.sun.xml.bind</groupId>
                <artifactId>jaxb-impl</artifactId>
                <version>${jaxb-version}</version>
            </dependency>

            <dependency>
                <groupId>javax.activation</groupId>
                <artifactId>activation</artifactId>
                <version>${javax-activation-version}</version>
            </dependency>

            <dependency>
                <groupId>org.mortbay.jetty</groupId>
                <artifactId>jetty</artifactId>
                <version>${jetty-version}</version>
            </dependency>

            <dependency>
                <groupId>net.sf.scannotation</groupId>
                <artifactId>scannotation</artifactId>
                <version>${scannotation-version}</version>
            </dependency>

            <dependency>
                <groupId>org.jboss.resteasy</groupId>
                <artifactId>resteasy-jaxrs</artifactId>
                <version>${resteasy-jaxrs-version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.scannotation</groupId>
                        <artifactId>scannotation</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>org.apache.zookeeper</groupId>
                <artifactId>zookeeper</artifactId>
                <version>${zookeeper-version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.sun.jmx</groupId>
                        <artifactId>jmxri</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.sun.jdmk</groupId>
                        <artifactId>jmxtools</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>javax.jms</groupId>
                        <artifactId>jms</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>junit</groupId>
                        <artifactId>junit</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.slf4j</groupId>
                        <artifactId>slf4j-log4j12</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>com.google.guava</groupId>
                <artifactId>guava</artifactId>
                <version>${guava-version}</version>
            </dependency>

            <dependency>
                <groupId>com.google.guava</groupId>
                <artifactId>listenablefuture</artifactId>
                <version>${guava-listenablefuture-version}</version>
            </dependency>

            <dependency>
                <groupId>com.google.guava</groupId>
                <artifactId>failureaccess</artifactId>
                <version>${guava-failureaccess-version}</version>
            </dependency>

            <dependency>
                <groupId>org.junit.jupiter</groupId>
                <artifactId>junit-jupiter-api</artifactId>
                <version>${junit-version}</version>
            </dependency>

            <dependency>
                <groupId>org.junit.jupiter</groupId>
                <artifactId>junit-jupiter-engine</artifactId>
                <version>${junit-version}</version>
            </dependency>

            <dependency>
                <groupId>com.facebook.swift</groupId>
                <artifactId>swift-codec</artifactId>
                <version>${swift-version}</version>
            </dependency>

            <dependency>
                <groupId>com.facebook.swift</groupId>
                <artifactId>swift-service</artifactId>
                <version>${swift-version}</version>
            </dependency>

            <dependency>
                <groupId>io.dropwizard</groupId>
                <artifactId>dropwizard-configuration</artifactId>
                <version>${dropwizard-version}</version>
            </dependency>

            <dependency>
                <groupId>io.dropwizard</groupId>
                <artifactId>dropwizard-logging</artifactId>
                <version>${dropwizard-version}</version>
            </dependency>

            <dependency>
                <groupId>io.dropwizard.metrics</groupId>
                <artifactId>metrics-core</artifactId>
                <version>${dropwizard-version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.slf4j</groupId>
                        <artifactId>slf4j-api</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>org.xerial.snappy</groupId>
                <artifactId>snappy-java</artifactId>
                <version>${snappy-version}</version>
            </dependency>

            <dependency>
                <groupId>org.awaitility</groupId>
                <artifactId>awaitility</artifactId>
                <version>${awaitility-version}</version>
                <scope>test</scope>
            </dependency>

        </dependencies>
    </dependencyManagement>

    <reporting>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-project-info-reports-plugin</artifactId>
            </plugin>

            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-javadoc-plugin</artifactId>
                <version>${maven-javadoc-plugin-version}</version>
                <configuration>
                    <additionalJOptions>
                        <additionalJOption>-J-Xmx1g</additionalJOption>
                    </additionalJOptions>
                    <failOnError>false</failOnError>
                </configuration>
            </plugin>

            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>clirr-maven-plugin</artifactId>
                <configuration>
                    <includes>
                        <include>org/apache/curator/**</include>
                    </includes>
                </configuration>
            </plugin>
        </plugins>
    </reporting>

    <build>
        <pluginManagement>
            <plugins>
                <plugin>
                    <groupId>org.apache.felix</groupId>
                    <artifactId>maven-bundle-plugin</artifactId>
                    <version>${maven-bundle-plugin-version}</version>
                </plugin>

                <plugin>
                    <groupId>org.commonjava.maven.plugins</groupId>
                    <artifactId>directory-maven-plugin</artifactId>
                    <version>0.1</version>
                </plugin>

                <plugin>
                    <groupId>com.mycila.maven-license-plugin</groupId>
                    <artifactId>maven-license-plugin</artifactId>
                    <version>${maven-license-plugin-version}</version>
                </plugin>

                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-release-plugin</artifactId>
                    <configuration>
                        <autoVersionSubmodules>true</autoVersionSubmodules>
                        <tagNameFormat>${project.artifactId}-${project.version}</tagNameFormat>
                        <pushChanges>false</pushChanges>
                        <localCheckout>true</localCheckout>
                    </configuration>
                </plugin>

                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-shade-plugin</artifactId>
                    <version>${maven-shade-plugin-version}</version>
                </plugin>

                <plugin>
                    <groupId>org.codehaus.mojo</groupId>
                    <artifactId>clirr-maven-plugin</artifactId>
                    <version>${clirr-maven-plugin-version}</version>
                </plugin>

                <plugin>
                    <groupId>org.codehaus.mojo</groupId>
                    <artifactId>build-helper-maven-plugin</artifactId>
                    <version>${build-helper-maven-plugin-version}</version>
                </plugin>

                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-dependency-plugin</artifactId>
                    <version>3.1.1</version>
                </plugin>
            </plugins>
        </pluginManagement>

        <resources>
            <resource>
                <directory>${basedir}</directory>
                <targetPath>META-INF</targetPath>
                <includes>
                    <include>DISCLAIMER</include>
                    <include>LICENSE</include>
                    <include>NOTICE</include>
                </includes>
            </resource>
        </resources>

        <plugins>
            <plugin>
                <groupId>org.apache.felix</groupId>
                <artifactId>maven-bundle-plugin</artifactId>
                <extensions>true</extensions>
                <inherited>true</inherited>
                <configuration>
                    <instructions>
                        <Bundle-Name>${project.name}</Bundle-Name>
                        <Bundle-SymbolicName>${project.artifactId}</Bundle-SymbolicName>
                        <Export-Package>${osgi.export.package}</Export-Package>
                        <Import-Package>${osgi.import.package}</Import-Package>
                        <DynamicImport-Package>${osgi.dynamic.import}</DynamicImport-Package>
                        <Private-Package>${osgi.private.package}</Private-Package>
                        <Require-Bundle>${osgi.require.bundle}</Require-Bundle>
                        <Bundle-Activator>${osgi.activator}</Bundle-Activator>
                        <Export-Service>${osgi.export.service}</Export-Service>
                    </instructions>
                    <supportedProjectTypes>
                        <supportedProjectType>jar</supportedProjectType>
                        <supportedProjectType>war</supportedProjectType>
                        <supportedProjectType>bundle</supportedProjectType>
                    </supportedProjectTypes>
                    <unpackBundle>true</unpackBundle>
                </configuration>
                <executions>
                    <execution>
                        <id>bundle-manifest</id>
                        <phase>process-classes</phase>
                        <goals>
                            <goal>manifest</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>

            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <version>3.0.0-M5</version>
                <configuration>
                    <threadCount>1</threadCount>
                    <reuseForks>false</reuseForks>
                    <redirectTestOutputToFile>${redirectTestOutputToFile}</redirectTestOutputToFile>
                    <rerunFailingTestsCount>2</rerunFailingTestsCount>
                </configuration>
            </plugin>

            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-javadoc-plugin</artifactId>
                <configuration>
                    <additionalJOptions>
                        <additionalJOption>-J-Xmx1g</additionalJOption>
                    </additionalJOptions>
                    <failOnError>false</failOnError>
                </configuration>
            </plugin>

            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-site-plugin</artifactId>
                <configuration>
                    <locales>en</locales>
                    <skipDeploy>true</skipDeploy>
                </configuration>
                <dependencies>
                    <dependency>
                        <groupId>org.apache.maven.doxia</groupId>
                        <artifactId>doxia-module-confluence</artifactId>
                        <version>${doxia-module-confluence-version}</version>
                    </dependency>
                </dependencies>
                <executions>
                    <execution>
                        <phase>site</phase>
                        <goals>
                            <goal>site</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>

            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-scm-publish-plugin</artifactId>
                <inherited>false</inherited>
                <configuration>
                    <checkinComment>Curator website deployment</checkinComment>
                    <!-- define curator-website-checkout-path in settings.xml -->
                    <checkoutDirectory>${curator-website-checkout-path}</checkoutDirectory>
                </configuration>
                <executions>
                    <execution>
                        <id>scm-publish</id>
                        <phase>site-deploy</phase>
                        <goals>
                            <goal>publish-scm</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>

            <!-- Directory plugin to find parent root directory absolute path -->
            <plugin>
                <groupId>org.commonjava.maven.plugins</groupId>
                <artifactId>directory-maven-plugin</artifactId>
                <version>0.1</version>
                <executions>
                    <execution>
                        <id>directories</id>
                        <goals>
                            <goal>highest-basedir</goal>
                        </goals>
                        <phase>validate</phase>
                        <configuration>
                            <property>main.basedir</property>
                        </configuration>
                    </execution>
                </executions>
            </plugin>

            <plugin>
                <groupId>com.mycila.maven-license-plugin</groupId>
                <artifactId>maven-license-plugin</artifactId>
                <configuration>
                    <header>${main.basedir}/src/etc/header.txt</header>
                    <excludes>
                        <exclude>**/merge-pr.py</exclude>
                        <exclude>**/*.confluence</exclude>
                        <exclude>**/*.confluence.vm</exclude>
                        <exclude>**/help.txt</exclude>
                        <exclude>**/*.rdf</exclude>
                        <exclude>**/.gitignore</exclude>
                        <exclude>**/*.thrift</exclude>
                        <exclude>**/*.json</exclude>
                        <exclude>**/.idea/**</exclude>
                        <exclude>**/DISCLAIMER</exclude>
                        <exclude>**/DEPENDENCIES</exclude>
                        <exclude>**/KEYS</exclude>
                        <exclude>**/LICENSE</exclude>
                        <exclude>**/NOTICE</exclude>
                        <exclude>**/README</exclude>
                        <exclude>**/CHANGES</exclude>
                        <exclude>**/RELEASE-NOTES</exclude>
                        <exclude>**/generated/**</exclude>
                    </excludes>
                    <strictCheck>true</strictCheck>
                </configuration>
                <executions>
                    <execution>
                        <id>license</id>
                        <phase>validate</phase>
                        <goals>
                            <goal>check</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>

            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-release-plugin</artifactId>
                <configuration>
                    <arguments>-DskipTests</arguments>
                    <mavenExecutorId>forked-path</mavenExecutorId>
                </configuration>
            </plugin>

            <plugin>
                <groupId>org.apache.rat</groupId>
                <artifactId>apache-rat-plugin</artifactId>
                <configuration>
                    <numUnapprovedLicenses>0</numUnapprovedLicenses>
                    <excludeSubProjects>false</excludeSubProjects>
                    <excludes>
                        <exclude>**/*.confluence</exclude>
                        <exclude>**/*.rdf</exclude>
                        <exclude>**/help.txt</exclude>
                        <exclude>**/.gitignore</exclude>
                        <exclude>**/*.thrift</exclude>
                        <exclude>**/*.json</exclude>
                        <exclude>**/.idea/**</exclude>
                        <exclude>**/DISCLAIMER</exclude>
                        <exclude>**/DEPENDENCIES</exclude>
                        <exclude>**/KEYS</exclude>
                        <exclude>**/LICENSE</exclude>
                        <exclude>**/NOTICE</exclude>
                        <exclude>**/README</exclude>
                        <exclude>**/CHANGES</exclude>
                        <exclude>**/RELEASE-NOTES</exclude>
                        <exclude>**/generated/**</exclude>
                    </excludes>
                </configuration>
            </plugin>

            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>clirr-maven-plugin</artifactId>
                <configuration>
                    <failOnError>false</failOnError>
                    <logResults>false</logResults>
                    <includes>
                        <include>org/apache/curator/**</include>
                    </includes>
                </configuration>
                <executions>
                    <execution>
                        <phase>compile</phase>
                        <goals>
                            <goal>check</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>

            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-shade-plugin</artifactId>
                <executions>
                    <execution>
                        <id>apache-curator-guava-shader</id>
                        <goals>
                            <goal>shade</goal>
                        </goals>
                        <phase>package</phase>
                        <configuration>
                            <createDependencyReducedPom>false</createDependencyReducedPom>
                            <relocations>
                                <relocation>
                                    <pattern>com.google</pattern>
                                    <shadedPattern>org.apache.curator.shaded.com.google</shadedPattern>
                                </relocation>
                            </relocations>
                            <artifactSet>
                                <includes>
                                    <include>${project.groupId}:${project.artifactId}</include>
                                </includes>
                            </artifactSet>
                            <filters>
                                <filter>
                                    <artifact>com.google.guava:guava</artifact>
                                    <excludes>
                                        <exclude>META-INF/**</exclude>
                                    </excludes>
                                </filter>
                                <filter>
                                    <artifact>com.google.guava:listenablefuture</artifact>
                                    <excludes>
                                        <exclude>META-INF/**</exclude>
                                    </excludes>
                                </filter>
                                <filter>
                                    <artifact>com.google.guava:failureaccess</artifact>
                                    <excludes>
                                        <exclude>META-INF/**</exclude>
                                    </excludes>
                                </filter>
                            </filters>
                        </configuration>
                    </execution>
                </executions>
            </plugin>

            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-antrun-plugin</artifactId>
                <executions>
                    <execution>
                        <phase>package</phase>
                        <goals>
                            <goal>run</goal>
                        </goals>
                        <configuration>
                            <target>
                                <condition property="skip-attaching-original-artifact" else="false">
                                    <not>
                                        <or>
                                            <equals arg1="${project.packaging}" arg2="jar" />
                                            <equals arg1="${project.packaging}" arg2="bundle" />
                                        </or>
                                    </not>
                                </condition>
                            </target>
                            <exportAntProperties>true</exportAntProperties>
                        </configuration>
                    </execution>
                </executions>
            </plugin>

            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>build-helper-maven-plugin</artifactId>
                <executions>
                    <execution>
                        <phase>package</phase>
                        <goals>
                            <goal>attach-artifact</goal>
                        </goals>
                        <configuration>
                            <artifacts>
                                <artifact>
                                    <file>${project.build.directory}/original-${project.build.finalName}.jar</file>
                                    <type>jar</type>
                                    <classifier>osgi</classifier>
                                </artifact>
                            </artifacts>
                            <skipAttach>${skip-attaching-original-artifact}</skipAttach>
                        </configuration>
                    </execution>
                </executions>
            </plugin>

            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-dependency-plugin</artifactId>
                <version>3.1.1</version>
            </plugin>
        </plugins>
    </build>

    <profiles>
        <profile>
            <id>jdk-8-minus</id>
            <activation>
                <jdk>(,1.8]</jdk>
            </activation>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-compiler-plugin</artifactId>
                        <configuration>
                            <source>${jdk-version}</source>
                            <target>${jdk-version}</target>
                        </configuration>
                    </plugin>
                </plugins>
            </build>
        </profile>
        <profile>
            <id>jdk-9-plus</id>
            <activation>
                <jdk>[1.9,)</jdk>
            </activation>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-compiler-plugin</artifactId>
                        <configuration>
                            <release>${short-jdk-version}</release>
                        </configuration>
                    </plugin>
                </plugins>
            </build>
        </profile>
        <profile>
            <id>staging-repo</id>
            <repositories>
                <repository>
                    <id>staging-repo</id>
                    <url>https://repository.apache.org/content/groups/staging/</url>
                </repository>
            </repositories>
        </profile>
    </profiles>
</project>
