<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <groupId>com.sqlancer</groupId>
  <artifactId>sqlancer</artifactId>
  <name>SQLancer</name>
  <version>2.0.0</version>
  <description>SQLancer finds logic bugs in Database Management Systems through automatic testing</description>
  <url>http://www.sqlancer.com/</url>
  <developers>
    <developer>
      <id>mrigger</id>
      <name>Manuel Rigger</name>
      <email><EMAIL></email>
      <url>https://www.manuelrigger.at/</url>
      <organization>ETH Zurich</organization>
      <organizationUrl>https://ethz.ch/</organizationUrl>
      <roles>
        <role>architect</role>
        <role>developer</role>
      </roles>
    </developer>
  </developers>
  <licenses>
    <license>
      <name>MIT License</name>
      <url>https://github.com/sqlancer/sqlancer/blob/master/LICENSE.md</url>
      <distribution>repo</distribution>
    </license>
  </licenses>
  <scm>
    <connection>scm:git:git://github.com/sqlancer/sqlancer.git</connection>
    <developerConnection>scm:git:ssh://github.com:sqlancer/sqlancer.git</developerConnection>
    <url>https://github.com/sqlancer/sqlancer/</url>
  </scm>
  <build>
    <sourceDirectory>src</sourceDirectory>
    <testSourceDirectory>test</testSourceDirectory>
    <plugins>
      <plugin>
        <artifactId>maven-shade-plugin</artifactId>
        <version>3.4.0</version>
        <executions>
          <execution>
            <phase>package</phase>
            <goals>
              <goal>shade</goal>
            </goals>
            <configuration>
              <artifactSet>
                <includes>
                  <include>com.beust:jcommander</include>
                </includes>
              </artifactSet>
            </configuration>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <artifactId>maven-antrun-plugin</artifactId>
        <version>3.1.0</version>
        <executions>
          <execution>
            <phase>package</phase>
            <goals>
              <goal>run</goal>
            </goals>
            <configuration>
              <target>
                <delete>
                  <fileset />
                </delete>
              </target>
            </configuration>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <artifactId>maven-surefire-plugin</artifactId>
        <version>2.22.2</version>
      </plugin>
      <plugin>
        <groupId>org.jacoco</groupId>
        <artifactId>jacoco-maven-plugin</artifactId>
        <version>0.8.8</version>
        <executions>
          <execution>
            <goals>
              <goal>prepare-agent</goal>
            </goals>
          </execution>
          <execution>
            <id>report</id>
            <phase>test</phase>
            <goals>
              <goal>report</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <artifactId>maven-compiler-plugin</artifactId>
        <version>3.10.1</version>
        <dependencies>
          <dependency>
            <groupId>org.codehaus.plexus</groupId>
            <artifactId>plexus-compiler-eclipse</artifactId>
            <version>2.13.0</version>
          </dependency>
          <dependency>
            <groupId>org.eclipse.jdt</groupId>
            <artifactId>ecj</artifactId>
            <version>3.28.0</version>
          </dependency>
          <dependency>
            <groupId>org.codehaus.plexus</groupId>
            <artifactId>plexus-compiler-api</artifactId>
            <version>2.13.0</version>
          </dependency>
        </dependencies>
        <configuration>
          <source>11</source>
          <target>11</target>
          <compilerArguments>
            <properties>${project.basedir}/.settings/org.eclipse.jdt.core.prefs</properties>
          </compilerArguments>
          <compilerId>eclipse</compilerId>
          <showWarnings>true</showWarnings>
          <failOnWarning>true</failOnWarning>
        </configuration>
      </plugin>
      <plugin>
        <groupId>net.revelc.code.formatter</groupId>
        <artifactId>formatter-maven-plugin</artifactId>
        <version>2.20.0</version>
        <executions>
          <execution>
            <id>eclipseformat</id>
            <phase>verify</phase>
            <goals>
              <goal>validate</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <artifactId>maven-dependency-plugin</artifactId>
        <version>3.4.0</version>
        <executions>
          <execution>
            <id>copy-dependencies</id>
            <phase>prepare-package</phase>
            <goals>
              <goal>copy-dependencies</goal>
            </goals>
            <configuration>
              <outputDirectory>${project.build.directory}/lib</outputDirectory>
              <overWriteReleases>false</overWriteReleases>
              <overWriteSnapshots>false</overWriteSnapshots>
              <overWriteIfNewer>true</overWriteIfNewer>
              <excludeArtifactIds>jcommander</excludeArtifactIds>
            </configuration>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <artifactId>maven-jar-plugin</artifactId>
        <version>3.3.0</version>
        <configuration>
          <forceCreation>true</forceCreation>
          <archive>
            <manifest>
              <addClasspath>true</addClasspath>
              <classpathPrefix>lib/</classpathPrefix>
              <mainClass>sqlancer.Main</mainClass>
            </manifest>
          </archive>
        </configuration>
      </plugin>
      <plugin>
        <groupId>org.codehaus.mojo</groupId>
        <artifactId>xml-maven-plugin</artifactId>
        <version>1.0.2</version>
        <executions>
          <execution>
            <id>xml-check-format</id>
            <phase>verify</phase>
            <goals>
              <goal>check-format</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <artifactId>maven-checkstyle-plugin</artifactId>
        <version>3.2.0</version>
        <executions>
          <execution>
            <id>checkstyle</id>
            <phase>verify</phase>
            <goals>
              <goal>check</goal>
            </goals>
            <configuration>
              <failOnViolation>true</failOnViolation>
            </configuration>
          </execution>
        </executions>
        <dependencies>
          <dependency>
            <groupId>com.puppycrawl.tools</groupId>
            <artifactId>checkstyle</artifactId>
            <version>10.5.0</version>
          </dependency>
        </dependencies>
        <configuration>
          <configLocation>configs/checkstyle.xml</configLocation>
        </configuration>
      </plugin>
      <plugin>
        <artifactId>maven-pmd-plugin</artifactId>
        <version>3.14.0</version>
        <executions>
          <execution>
            <id>pmd</id>
            <phase>verify</phase>
            <goals>
              <goal>check</goal>
            </goals>
            <configuration>
              <failurePriority>2</failurePriority>
              <failOnViolation>true</failOnViolation>
              <rulesets>
                <ruleset>configs/pmd-rules.xml</ruleset>
              </rulesets>
            </configuration>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <groupId>com.github.spotbugs</groupId>
        <artifactId>spotbugs-maven-plugin</artifactId>
        <version>4.7.3.0</version>
        <executions>
          <execution>
            <id>spotbugs</id>
            <phase>verify</phase>
            <goals>
              <goal>check</goal>
            </goals>
            <configuration>
              <excludeFilterFile>configs/spotbugs-exclude.xml</excludeFilterFile>
              <threshold>High</threshold>
            </configuration>
          </execution>
        </executions>
      </plugin>
    </plugins>
  </build>
  <profiles>
    <profile>
      <id>jdk-8-config</id>
      <properties>
        <javadocExecutable>${java.home}/../bin/javadoc</javadocExecutable>
      </properties>
    </profile>
    <profile>
      <id>jdk-11-config</id>
      <properties>
        <javadocExecutable>${java.home}/bin/javadoc</javadocExecutable>
      </properties>
    </profile>
    <profile>
      <id>release-steps</id>
      <build>
        <plugins>
          <plugin>
            <artifactId>maven-source-plugin</artifactId>
            <version>3.2.1</version>
            <executions>
              <execution>
                <id>attach-sources</id>
                <goals>
                  <goal>jar-no-fork</goal>
                </goals>
              </execution>
            </executions>
          </plugin>
          <plugin>
            <artifactId>maven-javadoc-plugin</artifactId>
            <version>3.4.1</version>
            <executions>
              <execution>
                <id>attach-javadocs</id>
                <goals>
                  <goal>jar</goal>
                </goals>
              </execution>
            </executions>
            <configuration>
              <source>8</source>
            </configuration>
          </plugin>
          <plugin>
            <artifactId>maven-gpg-plugin</artifactId>
            <version>3.0.1</version>
            <executions>
              <execution>
                <id>sign-artifacts</id>
                <phase>verify</phase>
                <goals>
                  <goal>sign</goal>
                </goals>
                <configuration>
                  <gpgArguments>
                    <arg>--pinentry-mode</arg>
                    <arg>loopback</arg>
                  </gpgArguments>
                </configuration>
              </execution>
            </executions>
          </plugin>
        </plugins>
      </build>
    </profile>
    <profile>
      <id>datafusion-tests</id>
      <build>
        <plugins>
          <plugin>
            <artifactId>maven-surefire-plugin</artifactId>
            <version>3.3.0</version>
            <configuration>
              <includes>
                <include>**/TestDataFusion.java</include>
              </includes>
              <argLine>--add-opens java.base/java.nio=org.apache.arrow.memory.core,ALL-UNNAMED</argLine>
            </configuration>
          </plugin>
        </plugins>
      </build>
    </profile>
  </profiles>
  <dependencies>
    <dependency>
      <groupId>com.google.auto.service</groupId>
      <artifactId>auto-service</artifactId>
      <version>1.0.1</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>org.postgresql</groupId>
      <artifactId>postgresql</artifactId>
      <version>42.5.1</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>com.ing.data</groupId>
      <artifactId>cassandra-jdbc-wrapper</artifactId>
      <version>4.7.0</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>com.yugabyte</groupId>
      <artifactId>jdbc-yugabytedb</artifactId>
      <version>42.3.5-yb-1</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>org.xerial</groupId>
      <artifactId>sqlite-jdbc</artifactId>
      <version>3.49.1.0</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>mysql</groupId>
      <artifactId>mysql-connector-java</artifactId>
      <version>8.0.30</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>org.mariadb.jdbc</groupId>
      <artifactId>mariadb-java-client</artifactId>
      <version>3.1.0</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>org.duckdb</groupId>
      <artifactId>duckdb_jdbc</artifactId>
      <version>1.3.0.0</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>com.facebook.presto</groupId>
      <artifactId>presto-jdbc</artifactId>
      <version>0.283</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>org.junit.jupiter</groupId>
      <artifactId>junit-jupiter-engine</artifactId>
      <version>5.11.2</version>
      <scope>test</scope>
      <exclusions>
        <exclusion>
          <artifactId>junit-platform-engine</artifactId>
          <groupId>org.junit.platform</groupId>
        </exclusion>
        <exclusion>
          <artifactId>junit-jupiter-api</artifactId>
          <groupId>org.junit.jupiter</groupId>
        </exclusion>
        <exclusion>
          <artifactId>apiguardian-api</artifactId>
          <groupId>org.apiguardian</groupId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>org.slf4j</groupId>
      <artifactId>slf4j-simple</artifactId>
      <version>2.0.6</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>ru.yandex.clickhouse</groupId>
      <artifactId>clickhouse-jdbc</artifactId>
      <version>0.3.2</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>com.h2database</groupId>
      <artifactId>h2</artifactId>
      <version>2.3.232</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>org.mongodb</groupId>
      <artifactId>mongodb-driver-sync</artifactId>
      <version>4.1.1</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>com.arangodb</groupId>
      <artifactId>arangodb-java-driver</artifactId>
      <version>6.9.0</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>org.questdb</groupId>
      <artifactId>questdb</artifactId>
      <version>6.5.3</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>org.hsqldb</groupId>
      <artifactId>hsqldb</artifactId>
      <version>2.7.1</version>
      <scope>runtime</scope>
    </dependency>
    <dependency>
      <groupId>org.apache.commons</groupId>
      <artifactId>commons-csv</artifactId>
      <version>1.9.0</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>com.github.jsqlparser</groupId>
      <artifactId>jsqlparser</artifactId>
      <version>4.6</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>org.apache.arrow</groupId>
      <artifactId>flight-sql-jdbc-driver</artifactId>
      <version>16.1.0</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>org.apache.hive</groupId>
      <artifactId>hive-jdbc</artifactId>
      <version>4.0.1</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>org.apache.hive</groupId>
      <artifactId>hive-serde</artifactId>
      <version>4.0.1</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>org.apache.hive</groupId>
      <artifactId>hive-cli</artifactId>
      <version>4.0.1</version>
      <scope>compile</scope>
    </dependency>
  </dependencies>
  <reporting>
    <plugins>
      <plugin>
        <artifactId>maven-jxr-plugin</artifactId>
        <version>3.3.0</version>
      </plugin>
    </plugins>
  </reporting>
  <distributionManagement>
    <repository>
      <id>ossrh</id>
      <name>Central Repository OSSRH</name>
      <url>https://oss.sonatype.org/service/local/staging/deploy/maven2/</url>
    </repository>
  </distributionManagement>
  <properties>
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
  </properties>
</project>
