package sqlancer.sqlite3.custom;

import java.sql.Connection;
import java.sql.Driver;
import java.sql.DriverPropertyInfo;
import java.sql.SQLException;
import java.sql.SQLFeatureNotSupportedException;
import java.util.Properties;
import java.util.logging.Logger;

/**
 * 自定义SQLite JDBC驱动包装器
 * 用于加载自定义编译的SQLite库
 */
public class CustomSQLiteDriver implements Driver {
    
    static {
        try {
            // 加载自定义SQLite库
            String customLibPath = System.getProperty("custom.sqlite.lib.path");
            if (customLibPath != null) {
                System.load(customLibPath);
                System.out.println("已加载自定义SQLite库: " + customLibPath);
            } else {
                // 尝试从标准位置加载
                System.loadLibrary("sqlite3_logged");
                System.out.println("已加载自定义SQLite库: sqlite3_logged");
            }
        } catch (UnsatisfiedLinkError e) {
            System.err.println("警告: 无法加载自定义SQLite库，将使用默认JDBC驱动");
            e.printStackTrace();
        }
        
        // 注册驱动
        try {
            java.sql.DriverManager.registerDriver(new CustomSQLiteDriver());
        } catch (SQLException e) {
            throw new RuntimeException("无法注册自定义SQLite驱动", e);
        }
    }
    
    private final Driver defaultDriver;
    
    public CustomSQLiteDriver() throws SQLException {
        // 使用默认的SQLite JDBC驱动作为后备
        this.defaultDriver = new org.sqlite.JDBC();
    }
    
    @Override
    public Connection connect(String url, Properties info) throws SQLException {
        if (!acceptsURL(url)) {
            return null;
        }
        
        System.out.println("使用自定义SQLite驱动连接: " + url);
        return defaultDriver.connect(url, info);
    }
    
    @Override
    public boolean acceptsURL(String url) throws SQLException {
        return url != null && url.startsWith("jdbc:sqlite:");
    }
    
    @Override
    public DriverPropertyInfo[] getPropertyInfo(String url, Properties info) throws SQLException {
        return defaultDriver.getPropertyInfo(url, info);
    }
    
    @Override
    public int getMajorVersion() {
        return defaultDriver.getMajorVersion();
    }
    
    @Override
    public int getMinorVersion() {
        return defaultDriver.getMinorVersion();
    }
    
    @Override
    public boolean jdbcCompliant() {
        return defaultDriver.jdbcCompliant();
    }
    
    @Override
    public Logger getParentLogger() throws SQLFeatureNotSupportedException {
        return defaultDriver.getParentLogger();
    }
}
