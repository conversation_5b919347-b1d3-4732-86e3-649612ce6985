package sqlancer.mariadb;

public final class MariaDBBugs {

    // https://jira.mariadb.org/browse/MDEV-21058
    public static boolean bug21058 = true;

    // https://jira.mariadb.org/browse/MDEV-32076
    public static boolean bug32076 = true;

    // https://jira.mariadb.org/browse/MDEV-32099
    public static boolean bug32099 = true;

    // https://jira.mariadb.org/browse/MDEV-32105
    public static boolean bug32105 = true;

    // https://jira.mariadb.org/browse/MDEV-32106
    public static boolean bug32106 = true;

    // https://jira.mariadb.org/browse/MDEV-32107
    public static boolean bug32107 = true;

    // https://jira.mariadb.org/browse/MDEV-32108
    public static boolean bug32108 = true;

    // https://jira.mariadb.org/browse/MDEV-32143
    public static boolean bug32143 = true;

    // https://jira.mariadb.org/browse/MDEV-33893
    public static boolean bug33893 = true;

    private MariaDBBugs() {
    }

}
