#!/bin/bash

# 完整的构建和使用脚本

set -e

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
cd "$SCRIPT_DIR"

# 步骤1: 构建Pass
echo "步骤1: 构建LLVM Pass..."
if [ ! -f "build/lib/libFunctionLoggerPass.so" ]; then
    ./build.sh
else
    echo "Pass已存在，跳过构建"
fi

echo ""

# 步骤2: 编译SQLite为LLVM IR
echo "步骤2: 将SQLite编译为LLVM IR..."
if [ ! -f "sqlite3.ll" ]; then
    echo "编译sqlite3.c为LLVM IR..."
    clang -fPIC -S -emit-llvm ../sqlite3.c -o sqlite3.ll -DSQLITE_THREADSAFE=0 -O2
    echo "生成sqlite3.ll完成"
else
    echo "sqlite3.ll已存在，跳过编译"
fi

echo ""

# 步骤3: 应用Pass
echo "步骤3: 应用函数日志Pass..."
echo "正在处理LLVM IR..."
opt -load-pass-plugin=./build/lib/libFunctionLoggerPass.so -passes="function-logger" sqlite3.ll -S -o sqlite3_logged.ll

echo "Pass应用完成，生成sqlite3_logged.ll"
echo ""

# 步骤4: 编译最终可执行文件
echo "步骤4: 编译最终可执行文件..."
clang -fPIC sqlite3_logged.ll ../shell.c -o sqlite3_logged -ldl -lpthread -lm

echo "编译完成！生成可执行文件: sqlite3_logged"